{"version": 3, "file": "request-logger.middleware.js", "sourceRoot": "", "sources": ["../../../src/core/middleware/request-logger.middleware.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA4D;AAE5D,iEAA6D;AAGtD,IAAM,uBAAuB,GAA7B,MAAM,uBAAuB;IAClC,YAA6B,cAA8B;QAA9B,mBAAc,GAAd,cAAc,CAAgB;IAAG,CAAC;IAE/D,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB;QACjD,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,EAAE,MAAM,EAAE,WAAW,EAAE,EAAE,EAAE,OAAO,EAAE,GAAG,GAAG,CAAC;QAEjD,GAAG,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;YACpB,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;YAC5C,MAAM,EAAE,UAAU,EAAE,GAAG,GAAG,CAAC;YAE3B,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,MAAM,EAAE,cAAc,EAAE;gBAC9C,MAAM;gBACN,GAAG,EAAE,WAAW;gBAChB,UAAU;gBACV,YAAY,EAAE,GAAG,YAAY,IAAI;gBACjC,EAAE;gBACF,SAAS,EAAE,OAAO,CAAC,YAAY,CAAC;aACjC,CAAC,CAAC;YAGH,IAAI,UAAU,IAAI,GAAG,EAAE,CAAC;gBACtB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,oBAAoB,EAAE;oBAC7C,MAAM;oBACN,GAAG,EAAE,WAAW;oBAChB,UAAU;oBACV,EAAE;oBACF,OAAO,EAAE;wBACP,YAAY,EAAE,OAAO,CAAC,YAAY,CAAC;wBACnC,iBAAiB,EAAE,OAAO,CAAC,iBAAiB,CAAC;wBAC7C,WAAW,EAAE,OAAO,CAAC,WAAW,CAAC;qBAClC;iBACF,CAAC,CAAC;YACL,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,IAAI,EAAE,CAAC;IACT,CAAC;CACF,CAAA;AAtCY,0DAAuB;kCAAvB,uBAAuB;IADnC,IAAA,mBAAU,GAAE;qCAEkC,gCAAc;GADhD,uBAAuB,CAsCnC"}
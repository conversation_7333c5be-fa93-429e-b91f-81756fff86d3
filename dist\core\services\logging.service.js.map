{"version": 3, "file": "logging.service.js", "sourceRoot": "", "sources": ["../../../src/core/services/logging.service.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAA4C;AAC5C,qCAA2D;AAGpD,IAAM,cAAc,GAApB,MAAM,cAAc;IAApB;QACG,WAAM,GAAG,IAAA,sBAAY,EAAC;YAC5B,MAAM,EAAE,gBAAM,CAAC,OAAO,CAAC,gBAAM,CAAC,SAAS,EAAE,EAAE,gBAAM,CAAC,IAAI,EAAE,CAAC;YACzD,UAAU,EAAE;gBACV,IAAI,oBAAU,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,gBAAgB,EAAE,KAAK,EAAE,OAAO,EAAE,CAAC;gBACnE,IAAI,oBAAU,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,mBAAmB,EAAE,CAAC;gBACtD,IAAI,oBAAU,CAAC,OAAO,CAAC;oBACrB,MAAM,EAAE,gBAAM,CAAC,OAAO,CAAC,gBAAM,CAAC,QAAQ,EAAE,EAAE,gBAAM,CAAC,MAAM,EAAE,CAAC;iBAC3D,CAAC;aACH;SACF,CAAC,CAAC;QAEK,gBAAW,GAAG,IAAA,sBAAY,EAAC;YACjC,MAAM,EAAE,gBAAM,CAAC,OAAO,CAAC,gBAAM,CAAC,SAAS,EAAE,EAAE,gBAAM,CAAC,IAAI,EAAE,CAAC;YACzD,UAAU,EAAE,CAAC,IAAI,oBAAU,CAAC,IAAI,CAAC,EAAE,QAAQ,EAAE,gBAAgB,EAAE,CAAC,CAAC;SAClE,CAAC,CAAC;IA0BL,CAAC;IAxBC,GAAG,CAAC,KAAa,EAAE,OAAe,EAAE,IAAU;QAC5C,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,KAAK,EAAE,OAAO,EAAE,IAAI,CAAC,CAAC;IACxC,CAAC;IAED,KAAK,CAAC,OAAe,EAAE,KAAc,EAAE,IAAU;QAC/C,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,KAAK,EAAE,GAAG,IAAI,EAAE,CAAC,CAAC;IACjD,CAAC;IAED,IAAI,CAAC,OAAe,EAAE,IAAU;QAC9B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IAClC,CAAC;IAED,KAAK,CAAC,OAAe,EAAE,IAAU;QAC/B,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;IACnC,CAAC;IAED,KAAK,CAAC,MAAc,EAAE,MAAc,EAAE,OAAY;QAChD,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,EAAE;YACjC,MAAM;YACN,MAAM;YACN,OAAO;YACP,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;IACL,CAAC;CACF,CAAA;AAzCY,wCAAc;yBAAd,cAAc;IAD1B,IAAA,mBAAU,GAAE;GACA,cAAc,CAyC1B"}
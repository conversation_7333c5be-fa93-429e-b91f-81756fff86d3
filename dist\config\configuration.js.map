{"version": 3, "file": "configuration.js", "sourceRoot": "", "sources": ["../../src/config/configuration.ts"], "names": [], "mappings": ";;AAAA,kBAAe,GAAG,EAAE,CAAC,CAAC;IACpB,GAAG,EAAE;QACH,IAAI,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC,IAAI,IAAI;QAC5C,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,KAAK;QAC1C,WAAW,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY,IAAI,uBAAuB;QAChE,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,IAAI,aAAa;QAC1C,GAAG,EAAE;YACH,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU;YAC9B,aAAa,EAAE,OAAO,CAAC,GAAG,CAAC,kBAAkB;YAC7C,UAAU,EAAE,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,KAAK;YAC/C,iBAAiB,EAAE,OAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI,IAAI;YAC7D,gBAAgB,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU;SACzC;QACD,QAAQ,EAAE;YACR,WAAW,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,EAAE,CAAC,IAAI,EAAE;YAClE,YAAY,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,sBAAsB,EAAE,EAAE,CAAC,IAAI,EAAE;YACpE,YAAY,EAAE,OAAO,CAAC,GAAG,CAAC,sBAAsB,KAAK,MAAM;SAC5D;KACF;IACD,QAAQ,EAAE;QACR,GAAG,EAAE,OAAO,CAAC,GAAG,CAAC,YAAY;KAC9B;IACD,KAAK,EAAE;QACL,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,qBAAqB;QACrD,IAAI,EAAE;YACJ,IAAI,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe;YACjC,QAAQ,EAAE,OAAO,CAAC,GAAG,CAAC,mBAAmB;SAC1C;KACF;IACD,UAAU,EAAE;QACV,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,qBAAqB;QAC5C,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,kBAAkB;QACtC,SAAS,EAAE,OAAO,CAAC,GAAG,CAAC,qBAAqB;KAC7C;IACD,SAAS,EAAE;QACT,QAAQ,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,oBAAoB,EAAE,EAAE,CAAC,IAAI,MAAM;QAClE,GAAG,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE,EAAE,CAAC,IAAI,GAAG;KACrD;IACD,GAAG,EAAE;QACH,aAAa,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,kBAAkB,EAAE,EAAE,CAAC,IAAI,EAAE;KAClE;IACD,KAAK,EAAE;QACL,iBAAiB,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE,EAAE,CAAC,IAAI,EAAE;KAC7E;IACD,OAAO,EAAE;QACP,iBAAiB,EAAE,OAAO,CAAC,GAAG,CAAC,mBAAmB;QAClD,oBAAoB,EAAE,OAAO,CAAC,GAAG,CAAC,sBAAsB;KACzD;IACD,OAAO,EAAE;QACP,KAAK,EAAE,OAAO,CAAC,GAAG,CAAC,SAAS,IAAI,MAAM;KACvC;IACD,QAAQ,EAAE;QACR,aAAa,EAAE,OAAO,CAAC,GAAG,CAAC,eAAe,IAAI,YAAY;KAC3D;CACF,CAAC,CAAC"}
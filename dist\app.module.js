"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AppModule = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const mongoose_1 = require("@nestjs/mongoose");
const user_module_1 = require("./modules/user/user.module");
const auth_module_1 = require("./modules/auth/auth.module");
const otp_module_1 = require("./modules/otp/otp.module");
const document_module_1 = require("./modules/document/document.module");
const application_module_1 = require("./modules/application/application.module");
const payment_module_1 = require("./modules/payment/payment.module");
const message_module_1 = require("./modules/message/message.module");
const notification_module_1 = require("./modules/notification/notification.module");
const email_module_1 = require("./modules/email/email.module");
const shared_module_1 = require("./shared/shared.module");
const student_module_1 = require("./modules/student/student.module");
const employer_module_1 = require("./modules/employer/employer.module");
const admin_module_1 = require("./modules/admin/admin.module");
const super_admin_module_1 = require("./modules/super-admin/super-admin.module");
let AppModule = class AppModule {
};
exports.AppModule = AppModule;
exports.AppModule = AppModule = __decorate([
    (0, common_1.Module)({
        imports: [
            config_1.ConfigModule.forRoot({
                isGlobal: true,
                envFilePath: '.env',
                load: [() => Promise.resolve().then(() => require('./config/configuration')).then(m => m.default())],
            }),
            mongoose_1.MongooseModule.forRootAsync({
                inject: [config_1.ConfigService],
                useFactory: (config) => ({
                    uri: process.env.DATABASE_URI || config.get("database.uri"),
                }),
            }),
            shared_module_1.SharedModule,
            user_module_1.UserModule,
            auth_module_1.AuthModule,
            otp_module_1.OTPModule,
            email_module_1.EmailModule,
            document_module_1.DocumentModule,
            application_module_1.ApplicationModule,
            payment_module_1.PaymentModule,
            message_module_1.MessageModule,
            notification_module_1.NotificationModule,
            student_module_1.StudentModule,
            employer_module_1.EmployerModule,
            admin_module_1.AdminModule,
            super_admin_module_1.SuperAdminModule,
        ],
    })
], AppModule);
//# sourceMappingURL=app.module.js.map
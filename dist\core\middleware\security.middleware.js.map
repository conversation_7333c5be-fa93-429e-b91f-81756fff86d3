{"version": 3, "file": "security.middleware.js", "sourceRoot": "", "sources": ["../../../src/core/middleware/security.middleware.ts"], "names": [], "mappings": ";;;;;;;;;AAAA,2CAA4D;AAE5D,mCAA4B;AAC5B,+BAA+B;AAGxB,IAAM,yBAAyB,GAA/B,MAAM,yBAAyB;IAA/B;QACG,qBAAgB,GAAG,IAAA,gBAAM,EAAC;YAChC,qBAAqB,EAAE;gBACrB,UAAU,EAAE;oBACV,UAAU,EAAE,CAAC,QAAQ,CAAC;oBACtB,QAAQ,EAAE,CAAC,QAAQ,EAAE,iBAAiB,CAAC;oBACvC,MAAM,EAAE,CAAC,QAAQ,EAAE,OAAO,EAAE,QAAQ,CAAC;oBACrC,SAAS,EAAE,CAAC,QAAQ,CAAC;iBACtB;aACF;YACD,yBAAyB,EAAE,IAAI;YAC/B,uBAAuB,EAAE,IAAI;YAC7B,yBAAyB,EAAE,EAAE,MAAM,EAAE,WAAW,EAAE;YAClD,kBAAkB,EAAE,IAAI;YACxB,UAAU,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE;YAC9B,aAAa,EAAE,IAAI;YACnB,IAAI,EAAE,IAAI;YACV,QAAQ,EAAE,IAAI;YACd,OAAO,EAAE,IAAI;YACb,cAAc,EAAE,EAAE,MAAM,EAAE,iCAAiC,EAAE;YAC7D,SAAS,EAAE,IAAI;SAChB,CAAC,CAAC;IAKL,CAAC;IAHC,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB;QACjD,IAAI,CAAC,gBAAgB,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;IACxC,CAAC;CACF,CAAA;AA1BY,8DAAyB;oCAAzB,yBAAyB;IADrC,IAAA,mBAAU,GAAE;GACA,yBAAyB,CA0BrC;AAGM,IAAM,cAAc,GAApB,MAAM,cAAc;IAApB;QACG,mBAAc,GAAG,KAAK,CAAC;YAC7B,MAAM,EAAE;gBACN,QAAQ,EAAE,IAAI;gBACd,MAAM,EAAE,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY;gBAC7C,QAAQ,EAAE,QAAQ;aACnB;SACF,CAAC,CAAC;IAKL,CAAC;IAHC,GAAG,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB;QACjD,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC;IACtC,CAAC;CACF,CAAA;AAZY,wCAAc;yBAAd,cAAc;IAD1B,IAAA,mBAAU,GAAE;GACA,cAAc,CAY1B"}
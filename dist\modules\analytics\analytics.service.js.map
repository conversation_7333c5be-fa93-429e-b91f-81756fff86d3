{"version": 3, "file": "analytics.service.js", "sourceRoot": "", "sources": ["../../../src/modules/analytics/analytics.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAA4C;AAC5C,+CAA+C;AAC/C,uCAAiC;AACjC,yDAA+C;AAC/C,0EAG2C;AAO3C,8DAAiE;AACjE,6DAIgC;AAChC,8DAAoD;AACpD,iEAA2D;AAC3D,qDAA2C;AAC3C,sCAAsC;AACtC,uCAAkC;AAG3B,IAAM,gBAAgB,GAAtB,MAAM,gBAAgB;IAC3B,YACuC,cAAgC,EAC9B,gBAAoC,EACxC,YAA4B,EAC5B,YAA4B,EACvB,aAAkC,EAC1C,SAAsB;QALjB,mBAAc,GAAd,cAAc,CAAkB;QAC9B,qBAAgB,GAAhB,gBAAgB,CAAoB;QACxC,iBAAY,GAAZ,YAAY,CAAgB;QAC5B,iBAAY,GAAZ,YAAY,CAAgB;QACvB,kBAAa,GAAb,aAAa,CAAqB;QAC1C,cAAS,GAAT,SAAS,CAAa;IACrD,CAAC;IAEJ,KAAK,CAAC,oBAAoB,CAAC,WAA2B;QACpD,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,CAAC;QAEnD,QAAQ,WAAW,CAAC,MAAM,EAAE,CAAC;YAC3B,KAAK,+BAAY,CAAC,GAAG;gBACnB,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YAChC,KAAK,+BAAY,CAAC,GAAG;gBACnB,OAAO,IAAI,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC;YAChC;gBACE,OAAO,IAAI,CAAC;QAChB,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,aAAa,CAAC,WAA2B;QACrD,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,WAAW,CAAC;QAEjD,QAAQ,IAAI,EAAE,CAAC;YACb,KAAK,6BAAU,CAAC,mBAAmB;gBACjC,OAAO,IAAI,CAAC,qBAAqB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YACxD,KAAK,6BAAU,CAAC,gBAAgB;gBAC9B,OAAO,IAAI,CAAC,kBAAkB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YACrD,KAAK,6BAAU,CAAC,eAAe;gBAC7B,OAAO,IAAI,CAAC,wBAAwB,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YAC3D,KAAK,6BAAU,CAAC,qBAAqB;gBACnC,OAAO,IAAI,CAAC,8BAA8B,CAAC,SAAS,EAAE,OAAO,CAAC,CAAC;YACjE;gBACE,MAAM,IAAI,KAAK,CAAC,qBAAqB,CAAC,CAAC;QAC3C,CAAC;IACH,CAAC;IAEO,WAAW,CAAC,IAA6B;QAC/C,MAAM,MAAM,GAAG,IAAI,iBAAM,EAAE,CAAC;QAC5B,OAAO,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAC5B,CAAC;IAEO,WAAW,CAAC,IAA6B;QAC/C,MAAM,GAAG,GAAG,IAAI,WAAW,EAAE,CAAC;QAC9B,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,GAAG,CAAC,EAAE,CAAC,MAAM,EAAE,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,CAAC;QAC3C,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,CAAC;QAC/D,GAAG,CAAC,QAAQ,EAAE,CAAC;QAEf,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,EAAE;YAC5C,GAAG,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,GAAG,GAAG,KAAK,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YAC1D,GAAG,CAAC,QAAQ,EAAE,CAAC;QACjB,CAAC,CAAC,CAAC;QAEH,GAAG,CAAC,GAAG,EAAE,CAAC;QACV,OAAO,MAAM,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IAChC,CAAC;IAED,KAAK,CAAC,sBAAsB;QAC1B,MAAM,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC;QACzB,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC,CAAC;QAE/D,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,qBAAqB,CAAC,SAAS,CAAC,CAAC;QAE5D,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,cAAc,CAAC;YACxC,IAAI,EAAE,SAAS;YACf,OAAO;SACR,CAAC,CAAC;QAEH,OAAO,SAAS,CAAC,IAAI,EAAE,CAAC;IAC1B,CAAC;IAED,KAAK,CAAC,uBAAuB,CAAC,SAAe,EAAE,OAAa;QAC1D,OAAO,IAAI,CAAC,cAAc;aACvB,IAAI,CAAC;YACJ,IAAI,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE;SACzC,CAAC;aACD,IAAI,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;IACvB,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,IAAU;QAC5C,MAAM,CAAC,UAAU,EAAE,YAAY,EAAE,QAAQ,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YAC7D,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC;YAClE,IAAI,CAAC,kBAAkB,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,IAAI,CAAC,WAAW,EAAE,CAAC;SAChE,CAAC,CAAC;QAEH,OAAO;YACL,UAAU;YACV,GAAG,YAAY;YACf,GAAG,QAAQ;SACZ,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,mBAAmB;QACvB,MAAM,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC;QACzB,MAAM,aAAa,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;QAEpE,MAAM,CAAC,eAAe,EAAE,UAAU,EAAE,kBAAkB,EAAE,cAAc,CAAC,GACrE,MAAM,OAAO,CAAC,GAAG,CAAC;YAChB,IAAI,CAAC,cAAc,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,CAAC;YAChD,IAAI,CAAC,YAAY,EAAE;YACnB,IAAI,CAAC,0BAA0B,EAAE;YACjC,IAAI,CAAC,iBAAiB,CAAC,aAAa,EAAE,KAAK,CAAC;SAC7C,CAAC,CAAC;QAEL,OAAO;YACL,cAAc,EAAE,eAAe,EAAE,OAAO,IAAI,EAAE;YAC9C,UAAU;YACV,kBAAkB;YAClB,cAAc;YACd,WAAW,EAAE,eAAe,EAAE,IAAI;SACnC,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,eAAe,CACnB,MAAc,EACd,MAAsC;QAEtC,MAAM,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;QAC3B,MAAM,SAAS,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;QAErD,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,cAAc;aACxC,IAAI,CAAC;YACJ,IAAI,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE;SACzC,CAAC;aACD,IAAI,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;QAErB,OAAO,IAAI,CAAC,wBAAwB,CAAC,SAAS,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;IAClE,CAAC;IAEO,qBAAqB,CAAC,MAAc;QAC1C,MAAM,IAAI,GAAG,IAAI,IAAI,EAAE,CAAC;QACxB,QAAQ,MAAM,EAAE,CAAC;YACf,KAAK,OAAO;gBACV,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC;gBAClC,MAAM;YACR,KAAK,QAAQ;gBACX,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC;gBAClC,MAAM;YACR,KAAK,SAAS;gBACZ,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,EAAE,CAAC,CAAC;gBACpC,MAAM;QACV,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEO,wBAAwB,CAC9B,SAAsB,EACtB,MAAc,EACd,MAAc;QAEd,OAAO,SAAS,CAAC,GAAG,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC;YAChC,IAAI,EAAE,MAAM,CAAC,IAAI;YACjB,KAAK,EAAE,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,IAAI,CAAC;SACnC,CAAC,CAAC,CAAC;IACN,CAAC;IAEO,KAAK,CAAC,qBAAqB,CAAC,SAAiB,EAAE,OAAe;QACpE,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;YACpD,SAAS,EAAE,EAAE,IAAI,EAAE,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE;SAClE,CAAC,CAAC;QAEH,MAAM,iBAAiB,GAAG,YAAY,CAAC,MAAM,CAAC;QAC9C,MAAM,YAAY,GAAG,YAAY,CAAC,MAAM,CACtC,CAAC,GAAsC,EAAE,GAAG,EAAE,EAAE;YAC9C,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YAC7C,OAAO,GAAG,CAAC;QACb,CAAC,EACD,EAAuC,CACxC,CAAC;QAEF,MAAM,cAAc,GAClB,CAAC,CAAC,YAAY,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,GAAG,iBAAiB,CAAC,GAAG,GAAG,CAAC;QAE/D,OAAO;YACL,iBAAiB;YACjB,kBAAkB,EAAE,YAAY;YAChC,cAAc;YACd,qBAAqB,EAAE,MAAM,IAAI,CAAC,8BAA8B,CAC9D,SAAS,EACT,OAAO,CACR;SACF,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,kBAAkB,CAAC,SAAiB,EAAE,OAAe;QACjE,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;YAC5C,SAAS,EAAE,EAAE,IAAI,EAAE,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE;YACjE,MAAM,EAAE,WAAW;SACpB,CAAC,CAAC;QAEH,MAAM,YAAY,GAAG,QAAQ,CAAC,MAAM,CAClC,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE,CAAC,GAAG,GAAG,OAAO,CAAC,MAAM,EACtC,CAAC,CACF,CAAC;QACF,MAAM,oBAAoB,GAAG;YAC3B,CAAC,4BAAW,CAAC,eAAe,CAAC,EAAE,CAAC;YAChC,CAAC,4BAAW,CAAC,cAAc,CAAC,EAAE,CAAC;YAC/B,CAAC,4BAAW,CAAC,gBAAgB,CAAC,EAAE,CAAC;SAClC,CAAC;QACF,MAAM,aAAa,GAAG,QAAQ,CAAC,MAAM,CACnC,CAAC,GAAgC,EAAE,OAAO,EAAE,EAAE;YAC5C,GAAG,CAAC,OAAO,CAAC,WAAW,CAAC;gBACtB,CAAC,GAAG,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC;YACnD,OAAO,GAAG,CAAC;QACb,CAAC,EACD,oBAAoB,CACrB,CAAC;QAEF,OAAO;YACL,YAAY;YACZ,aAAa;YACb,uBAAuB,EAAE,YAAY,GAAG,QAAQ,CAAC,MAAM;YACvD,gBAAgB,EAAE,QAAQ,CAAC,MAAM;SAClC,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,wBAAwB,CAAC,SAAiB,EAAE,OAAe;QACvE,MAAM,SAAS,GAAG;YAChB,SAAS,EAAE,EAAE,IAAI,EAAE,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE;SAClE,CAAC;QAEF,MAAM,CAAC,YAAY,EAAE,QAAQ,EAAE,SAAS,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YAC5D,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC,SAAS,CAAC;YAC/C,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC,SAAS,CAAC;YAC3C,IAAI,CAAC,aAAa,CAAC,cAAc,CAAC,SAAS,CAAC;SAC7C,CAAC,CAAC;QAEH,OAAO;YACL,eAAe,EAAE,YAAY;YAC7B,YAAY,EAAE,QAAQ;YACtB,iBAAiB,EAAE,SAAS;YAC5B,eAAe,EAAE,IAAI,CAAC,wBAAwB,CAC5C,YAAY,EACZ,QAAQ,EACR,SAAS,CACV;SACF,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,8BAA8B,CAC1C,SAAiB,EACjB,OAAe;QAEf,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;YAC9C,SAAS,EAAE,EAAE,IAAI,EAAE,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE;SAClE,CAAC,CAAC;QAEH,MAAM,cAAc,GAAG,SAAS,CAAC,MAAM,CAAC;QACxC,MAAM,iBAAiB,GAAG,SAAS,CAAC,MAAM,CACxC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,KAAK,UAAU,CACnC,CAAC,MAAM,CAAC;QACT,MAAM,iBAAiB,GAAG,SAAS,CAAC,MAAM,CACxC,CAAC,GAAG,EAAE,EAAE,CAAC,GAAG,CAAC,MAAM,KAAK,UAAU,CACnC,CAAC,MAAM,CAAC;QAET,OAAO;YACL,cAAc;YACd,iBAAiB;YACjB,iBAAiB;YACjB,gBAAgB,EAAE,CAAC,iBAAiB,GAAG,cAAc,CAAC,GAAG,GAAG;YAC5D,uBAAuB,EAAE,MAAM,IAAI,CAAC,gCAAgC,EAAE;SACvE,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,YAAY;QACxB,OAAO,IAAI,CAAC,SAAS,CAAC,cAAc,EAAE,CAAC;IACzC,CAAC;IAEO,KAAK,CAAC,0BAA0B;QACtC,OAAO,IAAI,CAAC,gBAAgB,CAAC,cAAc,CAAC;YAC1C,MAAM,EAAE,EAAE,IAAI,EAAE,CAAC,WAAW,EAAE,WAAW,CAAC,EAAE;SAC7C,CAAC,CAAC;IACL,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAAC,SAAe,EAAE,OAAa;QAC5D,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC;YAC5C,SAAS,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,OAAO,EAAE;YAC7C,MAAM,EAAE,WAAW;SACpB,CAAC,CAAC;QAEH,OAAO,QAAQ,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE,CAAC,GAAG,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC;IACpE,CAAC;IAEO,wBAAwB,CAC9B,YAAoB,EACpB,QAAgB,EAChB,SAAiB;QAGjB,MAAM,OAAO,GAAG;YACd,YAAY,EAAE,GAAG;YACjB,QAAQ,EAAE,GAAG;YACb,SAAS,EAAE,GAAG;SACf,CAAC;QAEF,OAAO,CACL,YAAY,GAAG,OAAO,CAAC,YAAY;YACnC,QAAQ,GAAG,OAAO,CAAC,QAAQ;YAC3B,SAAS,GAAG,OAAO,CAAC,SAAS,CAC9B,CAAC;IACJ,CAAC;IAEO,KAAK,CAAC,8BAA8B,CAC1C,SAAiB,EACjB,OAAe;QAEf,MAAM,qBAAqB,GAAG,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC;YAC7D,MAAM,EAAE,sCAAiB,CAAC,SAAS;YACnC,SAAS,EAAE,EAAE,IAAI,EAAE,IAAI,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,EAAE,IAAI,IAAI,CAAC,OAAO,CAAC,EAAE;SAClE,CAAC,CAAC;QAEH,IAAI,qBAAqB,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAEjD,MAAM,SAAS,GAAG,qBAAqB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;YAC1D,MAAM,iBAAiB,GAAG,GAAqC,CAAC;YAChE,OAAO,CACL,GAAG;gBACH,CAAC,iBAAiB,CAAC,SAAS,CAAC,OAAO,EAAE;oBACpC,iBAAiB,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CACzC,CAAC;QACJ,CAAC,EAAE,CAAC,CAAC,CAAC;QAEN,OAAO,SAAS,GAAG,qBAAqB,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IACrE,CAAC;IAEO,KAAK,CAAC,gCAAgC;QAC5C,MAAM,SAAS,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC;YAC9C,MAAM,EAAE,EAAE,GAAG,EAAE,CAAC,UAAU,EAAE,UAAU,CAAC,EAAE;SAC1C,CAAC,CAAC;QAEH,IAAI,SAAS,CAAC,MAAM,KAAK,CAAC;YAAE,OAAO,CAAC,CAAC;QAErC,MAAM,SAAS,GAAG,SAAS,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;YAC9C,MAAM,iBAAiB,GAAG,GAAqC,CAAC;YAChE,OAAO,CACL,GAAG;gBACH,CAAC,iBAAiB,CAAC,SAAS,CAAC,OAAO,EAAE;oBACpC,iBAAiB,CAAC,SAAS,CAAC,OAAO,EAAE,CAAC,CACzC,CAAC;QACJ,CAAC,EAAE,CAAC,CAAC,CAAC;QAEN,OAAO,SAAS,GAAG,SAAS,CAAC,MAAM,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IACzD,CAAC;CACF,CAAA;AA7VY,4CAAgB;2BAAhB,gBAAgB;IAD5B,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,sBAAW,EAAC,4BAAS,CAAC,IAAI,CAAC,CAAA;IAC3B,WAAA,IAAA,sBAAW,EAAC,gCAAW,CAAC,IAAI,CAAC,CAAA;IAC7B,WAAA,IAAA,sBAAW,EAAC,wBAAO,CAAC,IAAI,CAAC,CAAA;IACzB,WAAA,IAAA,sBAAW,EAAC,wBAAO,CAAC,IAAI,CAAC,CAAA;IACzB,WAAA,IAAA,sBAAW,EAAC,8BAAY,CAAC,IAAI,CAAC,CAAA;IAC9B,WAAA,IAAA,sBAAW,EAAC,kBAAI,CAAC,IAAI,CAAC,CAAA;qCAL8B,gBAAK;QACD,gBAAK;QACb,gBAAK;QACL,gBAAK;QACC,gBAAK;QACjB,gBAAK;GAPvC,gBAAgB,CA6V5B"}
import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Application } from './application.schema';
import { ApplicationStatus } from './enums/application-status.enum';
import { CreateApplicationDto, UpdateApplicationDto, StatusHistoryDto } from './dto/application.dto';
import { UserService } from '../user/user.service';
import { DocumentService } from '../document/document.service';

@Injectable()
export class ApplicationService {
  constructor(
    @InjectModel(Application.name)
    private applicationModel: Model<Application>,
    private readonly userService: UserService,
    private readonly documentService: DocumentService,
  ) {}

  async create(createApplicationDto: CreateApplicationDto): Promise<Application> {
    // Verify that both student and employer exist
    await this.userService.findById(createApplicationDto.studentId);
    await this.userService.findById(createApplicationDto.employerId);

    const application = await this.applicationModel.create({
      ...createApplicationDto,
      statusHistory: [{
        date: new Date(),
        status: ApplicationStatus.SUBMITTED,
        note: 'Application submitted'
      }]
    });

    return application;
  }

  async update(id: string, updateApplicationDto: UpdateApplicationDto): Promise<Application> {
    const application = await this.findOne(id);

    // Update basic
    if (!application) {
      throw new NotFoundException(`Application with ID ${id} not found`);
    }
    
    return application;
  }

  async create(studentId: string, createApplicationDto: CreateApplicationDto): Promise<Application> {
    // Verify employer exists
    await this.userService.findById(createApplicationDto.employerId);
    
    const newApplication = new this.applicationModel({
      studentId,
      employerId: createApplicationDto.employerId,
      position: createApplicationDto.position,
      status: ApplicationStatus.DRAFT,
      statusHistory: [`${new Date().toISOString()} - Application created`],
    });
    
    return newApplication.save();
  }

  async update(id: string, updateApplicationDto: UpdateApplicationDto): Promise<Application> {
    const application = await this.findOne(id);
    
    // Update fields
    if (updateApplicationDto.position) {
      application.position = updateApplicationDto.position;
    }
    
    if (updateApplicationDto.status && updateApplicationDto.status !== application.status) {
      application.status = updateApplicationDto.status;
      application.statusHistory.push({
        date: new Date(),
        status: updateApplicationDto.status,
        note: `Status updated to ${updateApplicationDto.status}`
      });
    }
    
    if (updateApplicationDto.interviewDate) {
      application.interviewDate = updateApplicationDto.interviewDate;
      application.statusHistory.push({
        date: new Date(),
        status: ApplicationStatus.INTERVIEW_SCHEDULED,
        note: `Interview scheduled for ${updateApplicationDto.interviewDate.toISOString()}`
      });
    }
    
    if (updateApplicationDto.interviewNotes) {
      application.interviewNotes = updateApplicationDto.interviewNotes;
    }
    
    if (updateApplicationDto.offerDetails) {
      application.offerDetails = updateApplicationDto.offerDetails;
      application.statusHistory.push({
        date: new Date(),
        status: ApplicationStatus.OFFER_EXTENDED,
        note: 'Offer details updated'
      });
    }
    
    return application.save();
  }

  async addDocument(id: string, documentId: string): Promise<Application> {
    const application = await this.findOne(id);
    
    // Verify document exists
    await this.documentService.findDocumentById(documentId);
    
    // Add document if not already added
    if (!application.documents.includes(documentId)) {
      application.documents.push(documentId);
      application.statusHistory.push(
        `${new Date().toISOString()} - Document added: ${documentId}`
      );
    }
    
    return application.save();
  }

  async delete(id: string): Promise<void> {
    const result = await this.applicationModel.deleteOne({ _id: id }).exec();
    
    if (result.deletedCount === 0) {
      throw new NotFoundException(`Application with ID ${id} not found`);
    }
  }
}

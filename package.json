{"name": "sea-faj-backend", "version": "0.1.0", "description": "Backend for SEA-FAJ Student & Employer Platform", "private": true, "scripts": {"prebuild": "<PERSON><PERSON><PERSON> dist", "build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "seed:super-admin": "ts-node src/scripts/seed-super-admin.ts"}, "dependencies": {"@nestjs/common": "^11.1.0", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.0", "@nestjs/jwt": "^11.0.0", "@nestjs/mongoose": "^11.0.3", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.0", "@nestjs/platform-socket.io": "^11.1.0", "@nestjs/swagger": "^11.2.0", "@nestjs/websockets": "^11.1.0", "@types/csurf": "^1.11.5", "@types/multer": "^1.4.12", "bcryptjs": "^3.0.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "cloudinary": "^2.6.1", "csurf": "^1.10.0", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.5.0", "helmet": "^6.2.0", "json2csv": "^6.0.0-alpha.2", "mongoose": "^8.15.0", "nodemailer": "^7.0.2", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "pdfkit": "^0.17.1", "reflect-metadata": "^0.2.2", "rimraf": "^6.0.1", "rxjs": "^7.8.2", "socket.io": "^4.8.1", "winston": "^3.17.0", "xss-clean": "^0.1.4"}, "devDependencies": {"@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^11.0.5", "@nestjs/testing": "^11.1.0", "@types/bcryptjs": "^3.0.0", "@types/express": "^5.0.1", "@types/jest": "^29.5.14", "@types/json2csv": "^5.0.7", "@types/node": "^22.15.14", "@types/nodemailer": "^6.4.17", "@types/passport-jwt": "^4.0.1", "@types/pdfkit": "^0.13.9", "@types/supertest": "^6.0.3", "@typescript-eslint/eslint-plugin": "^8.32.0", "@typescript-eslint/parser": "^8.32.0", "eslint": "^9.26.0", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.4.0", "jest": "^29.7.0", "prettier": "^3.5.3", "source-map-support": "^0.5.21", "supertest": "^7.1.0", "ts-jest": "^29.3.2", "ts-loader": "^9.5.2", "ts-node": "^10.9.2", "tsconfig-paths": "^4.2.0", "typescript": "^5.8.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}
{"version": 3, "file": "document.service.js", "sourceRoot": "", "sources": ["../../../src/modules/document/document.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAoF;AACpF,+CAA+C;AAC/C,uCAAiC;AACjC,2CAA8C;AAC9C,2CAA+C;AAC/C,uDAA+E;AAiBxE,IAAM,eAAe,GAArB,MAAM,eAAe;IAC1B,YAC0C,aAAkC,EAClE,aAA4B;QADI,kBAAa,GAAb,aAAa,CAAqB;QAClE,kBAAa,GAAb,aAAa,CAAe;QAEpC,eAAU,CAAC,MAAM,CAAC;YAChB,UAAU,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,uBAAuB,CAAC;YAC3D,OAAO,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,oBAAoB,CAAC;YACrD,UAAU,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,uBAAuB,CAAC;SAC5D,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,cAAc,CAClB,MAAc,EACd,IAAyB,EACzB,YAA0B;QAG1B,IACE,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAClB,yFAAyF,CAC1F,EACD,CAAC;YACD,MAAM,IAAI,4BAAmB,CAC3B,6DAA6D,CAC9D,CAAC;QACJ,CAAC;QAGD,IAAI,IAAI,CAAC,IAAI,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAE,CAAC;YAChC,MAAM,IAAI,4BAAmB,CAC3B,2CAA2C,CAC5C,CAAC;QACJ,CAAC;QAED,IAAI,CAAC;YAEH,MAAM,MAAM,GAAG,MAAM,IAAI,OAAO,CAAqB,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBACvE,MAAM,YAAY,GAAG,eAAU,CAAC,QAAQ,CAAC,aAAa,CACpD;oBACE,MAAM,EAAE,mBAAmB;oBAC3B,aAAa,EAAE,KAAK;oBACpB,eAAe,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,MAAM,CAAC;iBACxC,EACD,CAAC,KAAK,EAAE,MAAM,EAAE,EAAE;oBAChB,IAAI,KAAK;wBAAE,MAAM,CAAC,KAAK,CAAC,CAAC;;wBACpB,OAAO,CAAC,MAA4B,CAAC,CAAC;gBAC7C,CAAC,CACF,CAAC;gBAEF,YAAY,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC;YAGH,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;gBAC/C,MAAM;gBACN,YAAY,EAAE,IAAI,CAAC,YAAY;gBAC/B,YAAY,EAAE,MAAM,CAAC,SAAS;gBAC9B,aAAa,EAAE,MAAM,CAAC,UAAU;gBAChC,YAAY;gBACZ,MAAM,EAAE,gCAAc,CAAC,OAAO;aAC/B,CAAC,CAAC;YAEH,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAI,4BAAmB,CAAC,2BAA2B,CAAC,CAAC;QAC7D,CAAC;IACH,CAAC;IAED,KAAK,CAAC,oBAAoB,CACxB,UAAkB,EAClB,MAAsB,EACtB,eAAwB;QAExB,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC/D,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,4BAAmB,CAAC,oBAAoB,CAAC,CAAC;QACtD,CAAC;QAED,QAAQ,CAAC,MAAM,GAAG,MAAM,CAAC;QACzB,IAAI,MAAM,KAAK,gCAAc,CAAC,QAAQ,IAAI,eAAe,EAAE,CAAC;YAC1D,QAAQ,CAAC,eAAe,GAAG,eAAe,CAAC;QAC7C,CAAC;QAED,OAAO,QAAQ,CAAC,IAAI,EAAE,CAAC;IACzB,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,MAAc;QACnC,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,EAAE,MAAM,EAAE,CAAC,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IACrE,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,UAAkB;QACvC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC/D,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,0BAAiB,CAAC,oBAAoB,CAAC,CAAC;QACpD,CAAC;QACD,OAAO,QAAQ,CAAC;IAClB,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,UAAkB;QACrC,MAAM,QAAQ,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAC/D,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,MAAM,IAAI,0BAAiB,CAAC,oBAAoB,CAAC,CAAC;QACpD,CAAC;QAGD,MAAM,eAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;QAGzD,MAAM,QAAQ,CAAC,SAAS,EAAE,CAAC;IAC7B,CAAC;IAED,KAAK,CAAC,OAAO;QACX,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI,EAAE,CAAC,IAAI,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAC3D,CAAC;CACF,CAAA;AAnHY,0CAAe;0BAAf,eAAe;IAD3B,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,sBAAW,EAAC,8BAAY,CAAC,IAAI,CAAC,CAAA;qCAAwB,gBAAK;QACrC,sBAAa;GAH3B,eAAe,CAmH3B"}